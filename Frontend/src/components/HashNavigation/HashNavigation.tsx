'use client';

import React, { useEffect, useCallback } from 'react';
import { getCurrentHash, navigateToHash, onHashChange } from '@utils/hashNavigation';

interface HashNavigationProps {
  /**
   * Array of valid section IDs that can be navigated to on this page
   */
  validSections?: string[];
  /**
   * Callback function called when hash changes
   */
  onHashChange?: (hash: string) => void;
  /**
   * Whether to automatically scroll to hash on page load
   */
  autoScrollOnLoad?: boolean;
  /**
   * Scroll behavior for navigation
   */
  scrollBehavior?: 'smooth' | 'auto';
}

/**
 * HashNavigation Component
 * Handles client-side hash navigation for static sites
 * Ensures consistent behavior between local development and production
 */
export default function HashNavigation({
  validSections = [],
  onHashChange: onHashChangeCallback,
  autoScrollOnLoad = true,
  scrollBehavior = 'smooth'
}: HashNavigationProps) {
  
  const handleHashChange = useCallback((hash: string) => {
    // Call the callback if provided
    if (onHashChangeCallback) {
      onHashChangeCallback(hash);
    }
    
    // If we have valid sections defined, check if the hash is valid
    if (validSections.length > 0 && hash && !validSections.includes(hash)) {
      console.warn(`Hash navigation: Invalid section "${hash}". Valid sections:`, validSections);
      return;
    }
    
    // Navigate to the hash section
    if (hash) {
      navigateToHash(hash, scrollBehavior === 'smooth');
    }
  }, [onHashChangeCallback, validSections, scrollBehavior]);

  useEffect(() => {
    // Handle initial hash on component mount
    if (autoScrollOnLoad) {
      const initialHash = getCurrentHash();
      if (initialHash) {
        // Small delay to ensure DOM is ready
        setTimeout(() => {
          handleHashChange(initialHash);
        }, 100);
      }
    }

    // Set up hash change listener
    const cleanup = onHashChange(handleHashChange);
    
    // Cleanup on unmount
    return cleanup;
  }, [handleHashChange, autoScrollOnLoad]);

  // This component doesn't render anything visible
  return null;
}

/**
 * Hook for using hash navigation in components
 */
export function useHashNavigation() {
  const [currentHash, setCurrentHash] = React.useState<string>('');

  useEffect(() => {
    // Set initial hash
    setCurrentHash(getCurrentHash());

    // Set up hash change listener
    const cleanup = onHashChange((hash) => {
      setCurrentHash(hash);
    });

    return cleanup;
  }, []);

  const navigateToSection = useCallback((sectionId: string, smooth: boolean = true) => {
    navigateToHash(sectionId, smooth);
  }, []);

  return {
    currentHash,
    navigateToSection
  };
}
