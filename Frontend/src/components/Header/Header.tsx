'use client';

import { useCallback, useEffect, useState } from 'react';
import { Container, Nav, Navbar } from 'react-bootstrap';
import Image from 'next/image';
import Button from '@components/Button';
import Links from '@components/HeaderMegaMenu/Links';
import LinksWithBottomRightButton from '@components/HeaderMegaMenu/LinksWithBottomRightButton';
import LinksWithLatestBlogLink from '@components/HeaderMegaMenu/LinksWithLatestBlogLink';
import TitleWithLinks from '@components/HeaderMegaMenu/TitleWithLinks';
import DropdownIcon from '@components/Icons/DropdownIcon';
import HamburgerMenuIcon from '@components/Icons/HamburgerMenuIcon';
import CrossIcon from '@components/Icons/CrossIcon';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import styles from './Header.module.css';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function Header({ headerData }: any) {
  const [show, setShow] = useState(false); // Navbar visibility toggle
  const [lastScrollY, setLastScrollY] = useState(0); // Track last scroll position
  const [isNavbarCollapseMenuOpen, setIsNavbarCollapseMenuOpen] =
    useState(false);
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null); // Active mega menu

  const router = useRouter();

  useEffect(() => {
    // Reset the collapse menu state when navigating to a new page
    setIsNavbarCollapseMenuOpen(false);
  }, []);
  const controlNavbar = () => {
    if (typeof window !== 'undefined') {
      if (window.scrollY > lastScrollY) {
        setShow(true);
      } else {
        setShow(false);
      }

      // remember current page location to use in the next move
      setLastScrollY(window.scrollY);
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', controlNavbar);

      // cleanup function
      return () => {
        window.removeEventListener('scroll', controlNavbar);
      };
    }
  }, [lastScrollY, controlNavbar]);

  const handleCollapseClick = () => {
    setIsNavbarCollapseMenuOpen(!isNavbarCollapseMenuOpen);
  };

  const closeMegaMenu = () => {
    setActiveMegaMenu(null);
  };

  const {
    data: {
      attributes: {
        logo: {
          image: {
            data: {
              attributes: { url, height, width, alternativeText },
            },
          },
        },
        menu,
      },
    },
  } = headerData;

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-lg']})`,
  });

  const isMobile = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-sm-427']})`,
  });

  const renderMegaMenu = (
    menuTitle,
    menuLinks,
    button = undefined,
    titleDescription = undefined,
  ) => {
    const handleMegaMenuLinkClick = () => {
      setActiveMegaMenu(null);
      if (isTablet) {
        setIsNavbarCollapseMenuOpen(false);
      }
    };

    switch (menuTitle) {
      case 'Services':
        return (
          <TitleWithLinks
            menuArray={menuLinks}
            button={button}
            onClick={handleMegaMenuLinkClick}
          />
        );
      case 'ValueQuest':
        return (
          <LinksWithLatestBlogLink
            links={menuLinks}
            button={button}
            titleDescription={titleDescription}
            onClick={handleMegaMenuLinkClick}
          />
        );
      case 'Industries':
        return <Links links={menuLinks} onClick={handleMegaMenuLinkClick} />;
      case 'Resources':
        return (
          <LinksWithLatestBlogLink
            links={menuLinks}
            button={button}
            titleDescription={titleDescription}
            onClick={handleMegaMenuLinkClick}
          />
        );
      case 'About Us':
        return (
          <LinksWithBottomRightButton
            links={menuLinks}
            button={button}
            onClick={handleMegaMenuLinkClick}
          />
        );
      default:
        return '';
    }
  };

  return (
    <Navbar
      expand="lg"
      fixed="top"
      className={classNames(
        styles.navbarWrapper,
        show && styles.hidden,
        isNavbarCollapseMenuOpen && styles.hidden_override,
      )}
      data-crawler-ignore
    >
      <Container fluid className={styles.applicationContainer}>
        <Link href="/" prefetch={false}>
          <Navbar.Brand className={styles.brandContainer}>
            <Image
              src={url}
              height={28}
              width={150}
              className={classNames(
                'd-inline-block align-center',
                styles.mtech_logo,
              )}
              alt={alternativeText}
              priority
            />
          </Navbar.Brand>
        </Link>
        {isTablet && (
          <div className="d-flex">
            <Button
              type="button"
              label="Get In Touch"
              className={classNames(styles.button, isMobile && styles.hide)}
              onClick={() => {
                isNavbarCollapseMenuOpen && handleCollapseClick();
                router.push('/contact-us');
              }}
            />
            <Navbar.Toggle
              aria-controls="basic-navbar-nav"
              className={styles.navBarToggler}
              onClick={handleCollapseClick}
            >
              <HamburgerMenuIcon
                className={`${styles.menuIcons} ${isNavbarCollapseMenuOpen ? styles.clicked : ''}`}
              />
              <CrossIcon
                className={`${styles.menuIcons} ${!isNavbarCollapseMenuOpen ? styles.clicked : ''}`}
              />
            </Navbar.Toggle>
            <Link
              href="/search"
              aria-label="search"
              className={classNames(styles.search, styles.searchWrapper)}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/search_icon_3736bff546.svg`}
                width={24}
                height={25}
                alt="Search_icon"
              />
            </Link>
          </div>
        )}
        {isTablet ? (
          <div
            id="basic-navbar-nav"
            className={classNames(styles.navbarCollapse)}
            style={{ display: !isNavbarCollapseMenuOpen ? 'none' : 'block' }}
          >
            <Nav className={styles.nav}>
              {menu?.map((navMenu, index: number) => (
                <>
                  <div
                    key={index}
                    onMouseEnter={() => setActiveMegaMenu(navMenu?.title)}
                    onMouseLeave={closeMegaMenu}
                    className={styles.navItem}
                    data-title={navMenu?.title}
                  >
                    <Link
                      href={navMenu?.link}
                      prefetch={false}
                      passHref
                      legacyBehavior
                    >
                      <Nav.Link
                        className={classNames(styles.navLink)}
                        data-title={navMenu?.title}
                      >
                        {navMenu?.title}
                        <span className={styles.arrowIcon}>
                          <DropdownIcon />
                        </span>
                      </Nav.Link>
                    </Link>
                    {activeMegaMenu === navMenu?.title && (
                      <div
                        className={classNames(styles.megamenu, {
                          [styles.visible]: activeMegaMenu === navMenu?.title,
                        })}
                        data-title={navMenu?.title}
                      >
                        {renderMegaMenu(
                          navMenu?.title,
                          navMenu?.subMenu || navMenu?.subLinks,
                          navMenu?.button,
                          navMenu?.titleDescription,
                        )}
                      </div>
                    )}
                  </div>
                </>
              ))}
              <Button
                type="button"
                label="Get In Touch"
                className={classNames(styles.button, styles.visibility)}
                onClick={() => {
                  isNavbarCollapseMenuOpen && handleCollapseClick();
                  router.push('/contact-us');
                }}
              />
              <Link
                href="/search"
                aria-label="search"
                className={classNames(
                  styles.search,
                  styles.searchWrapper,
                  isTablet && styles.hide,
                )}
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/search_icon_3736bff546.svg`}
                  width={24}
                  height={25}
                  alt="Search_icon"
                />
              </Link>
            </Nav>
          </div>
        ) : (
          <Navbar.Collapse
            id="basic-navbar-nav"
            className={classNames(styles.navbarCollapse)}
          >
            <Nav className={styles.nav}>
              {menu?.map((navMenu, index: number) => (
                <div
                  key={index}
                  onMouseEnter={() => setActiveMegaMenu(navMenu?.title)}
                  onMouseLeave={closeMegaMenu}
                  className={styles.navItem}
                  data-title={navMenu?.title}
                >
                  <Link
                    href={navMenu?.link}
                    prefetch={false}
                    passHref
                    legacyBehavior
                  >
                    <Nav.Link
                      className={classNames(styles.navLink)}
                      data-title={navMenu?.title}
                    >
                      {navMenu?.title}
                      <span className={styles.arrowIcon}>
                        <DropdownIcon />
                      </span>
                    </Nav.Link>
                  </Link>

                  {activeMegaMenu === navMenu?.title && (
                    <div
                      className={classNames(styles.megamenu, {
                        [styles.visible]: activeMegaMenu === navMenu?.title,
                      })}
                      data-title={navMenu?.title}
                    >
                      {renderMegaMenu(
                        navMenu?.title,
                        navMenu?.subMenu || navMenu?.subLinks,
                        navMenu?.button,
                        navMenu?.titleDescription,
                      )}
                    </div>
                  )}
                </div>
              ))}
              <Button
                type="button"
                label="Get In Touch"
                className={classNames(styles.button, styles.visibility)}
                onClick={() => {
                  handleCollapseClick();
                  router.push('/contact-us');
                }}
              />
              <Link
                href="/search"
                aria-label="search"
                className={classNames(
                  styles.search,
                  styles.searchWrapper,
                  isTablet && styles.hide,
                )}
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/search_icon_3736bff546.svg`}
                  width={24}
                  height={25}
                  alt="Search_icon"
                />
              </Link>
            </Nav>
          </Navbar.Collapse>
        )}
      </Container>
    </Navbar>
  );
}
