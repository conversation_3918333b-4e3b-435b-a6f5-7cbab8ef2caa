'use client';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Link from 'next/link';
import DotButton from '@components/DotButton/DotButton';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './CaseStudyCard.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import classNames from '@utils/classNames';
import Heading from '@components/Heading';
import ImageWithSizing from '@components/ImageWithSizing';

export default function CaseStudyCard({ case_study, variantWhite = false }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    dragFree: true,
  });
  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);
  const isMobile = useMediaQueryState({
    query: `(max-width: 1285px)`,
  });
  const isDesktop = useMediaQueryState({
    query: `(min-width: 1286px)`,
  });
  return (
    <Container
      fluid
      className={`${styles.main_container} ${variantWhite ? styles.main_container_black : ''}`}
    >
      <div className={styles.inner_container}>
        <div className={styles.heading_box}>
          <Heading
            headingType="h2"
            title={case_study?.title}
            className={`${styles.title} ${variantWhite ? styles.title_black : ''}`}
          />
          <Link
            href={case_study?.link_url}
            className={`${styles.view_all_link} ${variantWhite ? styles.view_all_link_black : ''}`}
          >
            {case_study?.link_title}
          </Link>
        </div>
        {isDesktop && (
          <div className={styles.card_box}>
            <div
              className={`${styles.gradient_image_wrapper} ${styles.top_left}`}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Gredient_1_b44413eea5.png`}
                alt="Top Left Gradient"
                width={240}
                height={194}
                className={styles.reversed_image}
              />
            </div>
            <div
              className={`${styles.gradient_image_wrapper} ${styles.bottom_right}`}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Gredient_1_b44413eea5.png`}
                alt="Bottom Right Gradient"
                width={240}
                height={194}
              />
            </div>
            {case_study?.case_study_relation?.data?.map(data => (
              <Link
                className={styles.card}
                key={data?.id}
                href={data?.attributes?.preview?.link}
              >
                <div className={styles.overlay}>
                  <span className={styles.badge}>
                    {data?.attributes?.preview?.tag}
                  </span>
                  <Heading
                    headingType="h3"
                    title={data?.attributes?.preview?.title}
                    className={styles.box_title}
                  />
                  <div className={styles.expertise_delivered}>
                    {data?.attributes?.hero_section?.global_services?.data?.map(
                      data => (
                        <div className={styles.smallbox} key={data?.id}>
                          {data?.attributes?.service_title}
                        </div>
                      ),
                    )}
                  </div>
                </div>
                <div className={styles.arrow_button}>
                  <CircularButtonWithArrow variant="small" />
                </div>
                <ImageWithSizing
                  src={
                    data?.attributes?.preview?.preview_background_image?.data
                      ?.attributes
                  }
                  alt={
                    data?.attributes?.preview?.preview_background_image?.data
                      ?.attributes?.alternativeText
                  }
                  width={283}
                  height={500}
                  className={styles.image}
                />
              </Link>
            ))}
          </div>
        )}
        {isMobile && (
          <div className={styles.carousel}>
            <div
              className={`${styles.gradient_image_wrapper} ${styles.top_left}`}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Gredient_1_b44413eea5.png`}
                alt="Top Left Gradient"
                width={240}
                height={194}
                className={styles.reversed_image}
              />
            </div>
            <div
              className={`${styles.gradient_image_wrapper} ${styles.bottom_right}`}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Gredient_1_b44413eea5.png`}
                alt="Bottom Right Gradient"
                width={240}
                height={194}
              />
            </div>
            <section className={styles.embla}>
              <div className={styles.embla__viewport} ref={emblaRef}>
                <div className={styles.embla__container}>
                  {case_study?.case_study_relation.data?.map(data => (
                    <div className={styles.embla__slide} key={data?.id}>
                      <Link
                        className={styles.card}
                        key={data?.id}
                        href={data?.attributes?.preview?.link}
                      >
                        <div className={styles.overlay}>
                          <span className={styles.badge}>
                            {data?.attributes?.preview?.tag}
                          </span>
                          <Heading
                            headingType="h3"
                            title={data?.attributes?.preview?.title}
                            className={styles.box_title}
                          />
                          <div className={styles.expertise_delivered}>
                            {data?.attributes?.hero_section?.global_services?.data?.map(
                              data => (
                                <div className={styles.smallbox} key={data?.id}>
                                  {data?.attributes?.service_title}
                                </div>
                              ),
                            )}
                          </div>
                        </div>
                        <div className={styles.arrow_button}>
                          <CircularButtonWithArrow variant="small" />
                        </div>
                        <ImageWithSizing
                          src={
                            data?.attributes?.preview?.preview_background_image
                              ?.data?.attributes
                          }
                          alt={
                            data?.attributes?.preview?.preview_background_image
                              ?.data?.attributes?.alternativeText
                          }
                          width={283}
                          height={500}
                          className={styles.image}
                        />
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
              <div className={emblastyles.embla__controls}>
                <div className={emblastyles.embla__dots}>
                  {scrollSnaps.map((_, index) => (
                    <DotButton
                      key={index}
                      onClick={() => onDotButtonClick(index)}
                      className={
                        index === selectedIndex
                          ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                          : variantWhite
                            ? classNames(
                                emblastyles.embla__dot,
                                emblastyles.embla__dot_bg_white,
                              )
                            : emblastyles.embla__dot
                      }
                    />
                  ))}
                </div>
              </div>
            </section>
          </div>
        )}
      </div>
    </Container>
  );
}
