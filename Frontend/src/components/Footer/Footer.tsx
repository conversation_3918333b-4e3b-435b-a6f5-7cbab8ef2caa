'use client';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Link from 'next/link';
import HashLink from '@components/HashLink';
import Heading from '@components/Heading';

import styles from './Footer.module.css';

export default function Footer({ footerData }) {
  const props = footerData.data.attributes;
  return (
    <Container fluid className={styles.main_container} data-crawler-ignore>
      <Container className={styles.inner_container}>
        <div className={styles.first_second_row}>
          <div className={styles.firstrow}>
            {props?.sector_row?.map(data => (
              <div className={styles.column} key={data?.id}>
                <HashLink
                  href={data?.link || '/'}
                  key={data?.id}
                  className={styles.title_firstrow}
                >
                  <Heading headingType="h4" title={data?.title} />
                </HashLink>
                <ul className={styles.link_title}>
                  {data?.Sublinks.map(subsector => (
                    <li key={subsector?.id} className={styles.sublink_title}>
                      <HashLink
                        href={subsector?.link || '/'}
                        className={styles.sublink_title}
                      >
                        {subsector?.title}
                      </HashLink>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className={styles.secondrow}>
            {props?.pages_row?.map(data => (
              <div className={styles.column} key={data?.id}>
                <HashLink
                  href={data?.link || '/'}
                  key={data?.id}
                  className={styles.title_firstrow}
                >
                  <Heading headingType="h4" title={data?.title} />
                </HashLink>
                <ul className={styles.link_title}>
                  {data?.Sublinks.map(subsector => (
                    <li key={subsector?.id} className={styles.sublink_title}>
                      <HashLink
                        href={subsector?.link || '/'}
                        className={styles.sublink_title}
                      >
                        {subsector?.title}
                      </HashLink>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className={styles.thirdrow}>
          {props?.terms_and_condition_section.map(data => (
            <HashLink
              href={data?.link || '/'}
              className={styles.terms_and_condition_title}
              key={data?.id}
            >
              {data.title}
            </HashLink>
          ))}
        </div>
        <div className={styles.company_logo_section}>
          <Link
            href={props?.company_logo_section?.link || '/'}
            className={styles.imageContainer}
          >
            <Image
              src={props?.company_logo_section?.image?.data?.attributes?.url}
              alt="Logo Image"
              width={175}
              height={32}
            />
          </Link>
          <div className={styles.iconsContainer}>
            {props?.company_logo_section?.social_platforms.map(data => (
              <Link href={data?.link || '/'} key={data.id}>
                <Image
                  src={data?.image?.data?.attributes?.url}
                  alt="Icons"
                  width={40}
                  height={40}
                  quality={100}
                  className={styles.icon}
                />
              </Link>
            ))}
          </div>
        </div>
        <div className={styles.copyright}>
          {props?.company_logo_section?.Copyright}
        </div>
      </Container>
    </Container>
  );
}
