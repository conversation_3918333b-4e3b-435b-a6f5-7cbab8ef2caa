'use client';

import { useState } from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import CircularTagline from '@components/CircularTagline';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import VideoModal from '@components/VideoModal/VideoModal';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import emblastyles from '../../styles/emlaDots.module.css';
import styles from './Testimonial.module.css';
import { TestimonialSliderType, TestimonialTypes } from './types';
import ImageWithSizing from '@components/ImageWithSizing';

export default function Testimonial({ data }: TestimonialTypes) {
  const [show, setShow] = useState(false);
  const [showClose, setShowClose] = useState(false);

  const isMobile = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-sm-427']})`,
  });

  const [emblaRef, emblaApi] = useEmblaCarousel({
    dragFree: true,
    align: isMobile ? 'center' : 'start',
  });
  const [videoLink, setVideoLink] = useState('');

  const handleSlideClick = (slides: TestimonialSliderType) => {
    setVideoLink(slides?.testimonial_video_link);
    setShow(true);
    setShowClose(true);
  };

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-md-850']})`,
  });

  return (
    <div className={styles.testimonialWrapper}>
      <div className={styles.container}>
        <div className={styles.testimonialSection}>
          <div className={styles.testimonialHeading}>
            <Heading
              headingType="h2"
              position="left"
              className={styles.title}
              richTextValue={data.title}
            />
            <div className={styles.testimonialTagline}>
              <CircularTagline
                text_url={data?.circular_text_line_svg?.data.attributes.url}
                href={data?.tagline_url}
              />
            </div>
            {!isMobile && scrollSnaps.length > 1 && (
              <div
                className={classNames(
                  styles.embla__controls,
                  styles.desktopDots,
                )}
              >
                <div className={emblastyles.embla__dots}>
                  {scrollSnaps.map((_, index) => (
                    <DotButton
                      key={index}
                      onClick={() => onDotButtonClick(index)}
                      className={emblastyles.embla__dot.concat(
                        index === selectedIndex
                          ? `${` ${emblastyles.embla__dot_selected}`}`
                          : '',
                      )}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
          <div className={styles.emblaWrapper}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Gredient_1_b44413eea5.png`}
              width={240}
              height={197}
              alt="topLeftGradient"
              className={styles.bottomLeftGradient}
            />
            <div className={styles.embla}>
              <div className={styles.embla__viewport} ref={emblaRef}>
                <div
                  className={classNames(
                    styles.sliderContainer,
                    styles.embla__container,
                  )}
                >
                  {data?.testimonials_slider?.map(
                    (slides: TestimonialSliderType) => (
                      <div
                        className={classNames(
                          styles.slide,
                          styles.embla__slide,
                        )}
                        onClick={() => handleSlideClick(slides)}
                        role="presentation"
                        key={slides?.id}
                      >
                        <ImageWithSizing
                          src={slides?.image?.data?.attributes}
                          alt={slides?.image?.data?.attributes?.alternativeText}
                          width={387}
                          height={300}
                          className={styles.img}
                        />
                        <div className={styles.clientTestiMonial}>
                          <div className={styles.playBtn}>
                            <Image
                              src={
                                data?.testimonial_playbtn_logo?.data?.attributes
                                  ?.url
                              }
                              width={
                                data?.testimonial_playbtn_logo?.data?.attributes
                                  ?.width
                              }
                              height={
                                data?.testimonial_playbtn_logo?.data?.attributes
                                  ?.height
                              }
                              alt="play-button"
                            />
                          </div>
                          <div className={styles.clientInfo}>
                            <Heading
                              className={styles.clientName}
                              headingType="h3"
                              position="left"
                              title={slides?.clientName}
                            />

                            <div
                              className={styles.clientDesignation}
                              // eslint-disable-next-line react/no-danger
                              dangerouslySetInnerHTML={{
                                __html: slides?.clientDescription,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ),
                  )}
                </div>
              </div>
              {isTablet && (
                <div
                  className={classNames(
                    styles.embla__controls,
                    styles.mobileDots,
                  )}
                >
                  <div className={emblastyles.embla__dots}>
                    {scrollSnaps.length > 1 &&
                      scrollSnaps.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={emblastyles.embla__dot.concat(
                            index === selectedIndex
                              ? `${` ${emblastyles.embla__dot_selected}`}`
                              : '',
                          )}
                        />
                      ))}
                  </div>
                </div>
              )}
            </div>
            <Image
              src={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Group_5042_1_5259ae27e3.svg`}
              width={240}
              height={197}
              alt="topRightGradient"
              className={styles.topRightGradient}
            />
          </div>
        </div>
        <VideoModal
          show={show}
          setShow={setShow}
          showClose={showClose}
          setShowClose={setShowClose}
          videoLink={videoLink}
        />
      </div>
    </div>
  );
}
