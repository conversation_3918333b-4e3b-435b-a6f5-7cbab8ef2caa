import CircularTagline from '@components/CircularTagline';
import variableStyles from '@styles/variables.module.css';

export default {
  title: 'Components/Utility/CirularTagline',
};

export function CircularTaglineStory() {
  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
        background: variableStyles.colorBlack,
        textAlign: 'center',
      }}
    >
      <CircularTagline
        text_url={`${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/Group_26_348828df5c.svg`}
      />
    </div>
  );
}
