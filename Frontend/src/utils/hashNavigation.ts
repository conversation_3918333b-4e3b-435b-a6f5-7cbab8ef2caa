/**
 * Hash Navigation Utility
 * Handles hash-based navigation consistently across local development and production environments
 * Accounts for Next.js static export with trailingSlash: true and CloudFront behavior
 */

/**
 * Normalizes a URL to ensure consistent behavior between local and production
 * @param url - The URL to normalize
 * @returns Normalized URL with proper trailing slash handling
 */
export function normalizeUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }

  // Remove any existing hash fragments for base URL normalization
  const [baseUrl] = url.split('#');
  
  // Ensure trailing slash for consistency with Next.js trailingSlash: true
  const normalizedBase = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
  
  return normalizedBase;
}

/**
 * Creates a proper hash URL for navigation
 * @param basePath - The base path (e.g., '/about-us', '/careers')
 * @param hashFragment - The hash fragment (e.g., 'our-story', 'values')
 * @returns Properly formatted hash URL
 */
export function createHashUrl(basePath: string, hashFragment: string): string {
  if (!basePath || !hashFragment) {
    return basePath || '';
  }

  const normalizedBase = normalizeUrl(basePath);
  const cleanHash = hashFragment.startsWith('#') ? hashFragment.slice(1) : hashFragment;
  
  return `${normalizedBase}#${cleanHash}`;
}

/**
 * Extracts the current hash fragment from the URL
 * @returns The current hash fragment without the # symbol, or empty string if none
 */
export function getCurrentHash(): string {
  if (typeof window === 'undefined') {
    return '';
  }
  
  return window.location.hash.substring(1);
}

/**
 * Navigates to a hash section on the current page
 * @param hashFragment - The hash fragment to navigate to
 * @param smooth - Whether to use smooth scrolling (default: true)
 */
export function navigateToHash(hashFragment: string, smooth: boolean = true): void {
  if (typeof window === 'undefined' || !hashFragment) {
    return;
  }

  const cleanHash = hashFragment.startsWith('#') ? hashFragment.slice(1) : hashFragment;
  
  // Update the URL hash
  if (window.location.hash !== `#${cleanHash}`) {
    window.location.hash = `#${cleanHash}`;
  }
  
  // Scroll to the element
  const element = document.getElementById(cleanHash);
  if (element) {
    element.scrollIntoView({ 
      behavior: smooth ? 'smooth' : 'auto',
      block: 'start'
    });
  }
}

/**
 * Navigates to a different page with a hash fragment
 * @param basePath - The base path to navigate to
 * @param hashFragment - The hash fragment to navigate to
 */
export function navigateToPageWithHash(basePath: string, hashFragment: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  const hashUrl = createHashUrl(basePath, hashFragment);
  window.location.href = hashUrl;
}

/**
 * Checks if the current page matches the given base path
 * @param basePath - The base path to check against
 * @returns True if the current page matches the base path
 */
export function isCurrentPage(basePath: string): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  const currentPath = window.location.pathname;
  const normalizedBasePath = normalizeUrl(basePath);
  const normalizedCurrentPath = normalizeUrl(currentPath);
  
  return normalizedCurrentPath === normalizedBasePath;
}

/**
 * Handles hash navigation for links
 * Determines whether to navigate to a new page or scroll to a section on the current page
 * @param href - The full href including base path and hash
 * @param onClick - Optional click handler to call
 */
export function handleHashNavigation(href: string, onClick?: () => void): void {
  if (!href || typeof href !== 'string') {
    return;
  }

  // Call the onClick handler if provided
  if (onClick) {
    onClick();
  }

  // Parse the href to extract base path and hash
  const [basePath, hashFragment] = href.split('#');
  
  if (!hashFragment) {
    // No hash fragment, just navigate normally
    if (typeof window !== 'undefined') {
      window.location.href = href;
    }
    return;
  }

  // Check if we're navigating to the same page
  if (isCurrentPage(basePath)) {
    // Same page, just scroll to the hash section
    navigateToHash(hashFragment);
  } else {
    // Different page, navigate with hash
    navigateToPageWithHash(basePath, hashFragment);
  }
}

/**
 * Clears any existing hash from the current URL
 */
export function clearHash(): void {
  if (typeof window === 'undefined') {
    return;
  }

  if (window.location.hash) {
    // Use replaceState to avoid adding to browser history
    const urlWithoutHash = window.location.href.split('#')[0];
    window.history.replaceState(null, '', urlWithoutHash);
  }
}

/**
 * Sets up hash change event listener
 * @param callback - Function to call when hash changes
 * @returns Cleanup function to remove the event listener
 */
export function onHashChange(callback: (hash: string) => void): () => void {
  if (typeof window === 'undefined') {
    return () => {};
  }

  const handleHashChange = () => {
    const currentHash = getCurrentHash();
    callback(currentHash);
  };

  window.addEventListener('hashchange', handleHashChange);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('hashchange', handleHashChange);
  };
}
