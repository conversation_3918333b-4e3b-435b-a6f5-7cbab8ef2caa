/**
 * Extracts section ID from a link containing hash fragment
 * Handles both local development and production URL formats
 * @param link - The link containing hash fragment (e.g., "/careers/#values" or "/about-us/#our-story")
 * @returns The section ID without hash symbol
 */
export default function getSectionId(link: string): string {
  if (!link || typeof link !== 'string') {
    return '';
  }

  // Split by hash and get the fragment
  const parts = link.split('#');
  if (parts.length < 2) {
    return '';
  }

  // Return the hash fragment, trimmed of any whitespace
  return parts[1].trim();
}
