# ------------------------
# CloudFront OAC
# ------------------------
resource "aws_cloudfront_origin_access_control" "this" {
  name                              = "mtl-site-oac${var.isProd}"
  description                       = "CloudFront OAC for secure access to S3-${var.environment}"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

# ------------------------
# CloudFront Response Header Policy (Security Headers)
# ------------------------
# resource "aws_cloudfront_response_headers_policy" "mtl_security_headers" {
#   name = "MTL-Security-Headers-Policy"
#   comment = "Security headers for MTL CloudFront distribution including HSTS, CSP, and frame protection"

#   security_headers_config {
#     strict_transport_security {
#       access_control_max_age_sec = 31536000
#       include_subdomains         = true
#       preload                    = true
#       override                   = true
#     }

#     frame_options {
#       frame_option = "DENY"
#       override     = true
#     }

#     content_type_options {
#       override = true
#     }

#     content_security_policy {
#       content_security_policy = "img-src 'self' data: storage.googleapis.com cdn-gcp.marutitech.com cdn-gcp.new.marutitech.com cdn.marutitech.com px.ads.linkedin.com i.ytimg.com c.clarity.ms b.6sc.co track.hubspot.com; frame-src 'self' data: www.youtube.com www.googletagmanager.com; object-src 'none';"
#       override                = true
#     }

#   }
#   custom_headers_config {
#     items {
#       header   = "cross_origin_opener_policy"
#       override = true
#       value    = "same-origin"
#     }

#   }
# }

data "aws_cloudfront_cache_policy" "caching_optimized" {
  name = "Managed-CachingOptimized"
}

data "aws_cloudfront_cache_policy" "caching_disabled" {
  name = "Managed-CachingDisabled"
}

# ------------------------
# Origin Request Policy
# ------------------------
# resource "aws_cloudfront_origin_request_policy" "api_gateway_origin_policy" {
#   name    = "API-Gateway-Origin-Request-Policy"
#   comment = "Allows forwarding of all query strings to API Gateway"

#   headers_config {
#     header_behavior = "none"
#   }

#   query_strings_config {
#     query_string_behavior = "all"
#   }

#   cookies_config {
#     cookie_behavior = "none"
#   }
# }

data "aws_cloudfront_origin_request_policy" "api_gateway_origin_policy" {
  name = "API-Gateway-Origin-Request-Policy"
}

data "aws_cloudfront_response_headers_policy" "mtl_security_headers" {
  name = "MTL-Security-Headers-Policy"
}

# ------------------------
# CloudFront Distribution
# ------------------------
resource "aws_cloudfront_distribution" "mtl-cloudfront" {
  enabled             = true
  default_root_object = "index.html"

  custom_error_response {
    error_code         = 403
    response_code      = 404
    response_page_path = "/404.html"
  }

  custom_error_response {
    error_code         = 404
    response_code      = 404
    response_page_path = "/404.html"
  }

  origin {
    domain_name              = aws_s3_bucket.static_site.bucket_regional_domain_name
    origin_id                = "s3-origin-${aws_s3_bucket.static_site.id}"
    origin_access_control_id = aws_cloudfront_origin_access_control.this.id
  }

  origin {
    domain_name = "${aws_api_gateway_rest_api.maruti_site_api.id}.execute-api.${var.aws_region}.amazonaws.com"
    origin_id   = "api-gateway-origin"

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }

  }

  ordered_cache_behavior {
    path_pattern     = "/sendToSlack"
    target_origin_id = "api-gateway-origin"

    allowed_methods = ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"]
    cached_methods  = ["GET", "HEAD"]

    viewer_protocol_policy     = "redirect-to-https"
    cache_policy_id            = data.aws_cloudfront_cache_policy.caching_disabled.id
    origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.api_gateway_origin_policy.id
    response_headers_policy_id = data.aws_cloudfront_response_headers_policy.mtl_security_headers.id
  }

  default_cache_behavior {
    target_origin_id = "s3-origin-${aws_s3_bucket.static_site.id}"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]

    compress = true # ENABLE GZIP/BROTLI COMPRESSION

    viewer_protocol_policy     = "redirect-to-https"
    response_headers_policy_id = data.aws_cloudfront_response_headers_policy.mtl_security_headers.id

    cache_policy_id = data.aws_cloudfront_cache_policy.caching_optimized.id

    lambda_function_association {
      event_type   = "viewer-request"
      lambda_arn   = "${aws_lambda_function.edge_redirect.arn}:${aws_lambda_function.edge_redirect.version}"
      include_body = false
    }

  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  # CloudFront Custom Domain (alias)
  aliases = [var.cloudfront_custom_domain_name]

  viewer_certificate {
    acm_certificate_arn      = var.cloudfront_certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  comment = "CloudFront distribution for Maruti Tech site (${var.environment})"
  tags = merge(
    var.common_tags,
    {
      Name = "Maruti Tech Site - ${var.environment}"
    }
  )
}

# ------------------------
# CloudFront OAC  for maruti-site-cdn
# ------------------------
# resource "aws_cloudfront_origin_access_control" "oac" {
#   name                              = "cdn-oac-maruti-site-cdn"
#   description                       = "OAC for maruti-site-cdn"
#   origin_access_control_origin_type = "s3"
#   signing_behavior                  = "always"
#   signing_protocol                  = "sigv4"
# }

# -----------------------------
# Response Headers Policy (set Cache-Control)
# -----------------------------
# resource "aws_cloudfront_response_headers_policy" "static_assets" {
#   name    = "static-assets-headers-policy"
#   comment = "Sets long-term Cache-Control header for static assets to improve caching performance"

#   custom_headers_config {
#     items {
#       header   = "Cache-Control"
#       override = true
#       value    = "public, max-age=9999999999, must-revalidate"
#     }
#   }
# }

# ------------------------
# CloudFront Distribution
# ------------------------
# resource "aws_cloudfront_distribution" "cdn" {
#   enabled             = true
#   default_root_object = "index.html"

#   origin {
#     domain_name              = aws_s3_bucket.maruti_site_cdn.bucket_regional_domain_name
#     origin_id                = "s3-origin-${aws_s3_bucket.maruti_site_cdn.id}"
#     origin_access_control_id = aws_cloudfront_origin_access_control.oac.id
#   }

#   default_cache_behavior {
#     target_origin_id = "s3-origin-${aws_s3_bucket.maruti_site_cdn.id}"
#     allowed_methods  = ["GET", "HEAD"]
#     cached_methods   = ["GET", "HEAD"]

#     viewer_protocol_policy = "redirect-to-https"

#     forwarded_values {
#       query_string = false
#       cookies {
#         forward = "none"
#       }
#     }
#   }
#   dynamic "ordered_cache_behavior" {
#     for_each = toset(var.image_extensions)
#     content {
#       path_pattern           = "/*.${ordered_cache_behavior.key}"
#       target_origin_id       = "s3-origin-maruti-site-cdn"
#       viewer_protocol_policy = "redirect-to-https"

#       allowed_methods = ["GET", "HEAD"]
#       cached_methods  = ["GET", "HEAD"]

#       compress = true

#       cache_policy_id            = data.aws_cloudfront_cache_policy.caching_optimized.id
#       response_headers_policy_id = aws_cloudfront_response_headers_policy.static_assets.id
#     }
#   }
#   price_class = "PriceClass_100"

#   restrictions {
#     geo_restriction {
#       restriction_type = "none"
#     }
#   }

#   # CloudFront CDN Custom Domain (alias) 
#   aliases = [var.cloudfront_cdn_custom_domain_name]

#   viewer_certificate {
#     acm_certificate_arn      = var.certificate_arn
#     ssl_support_method       = "sni-only"
#     minimum_protocol_version = "TLSv1.2_2021"
#   }

#   comment = "CloudFront distribution for Maruti Tech static CDN assets"
#   tags = {
#     Name      = "Maruti Tech Static CDN assets",
#     ManagedBy = "Terraform"
#   }
# }