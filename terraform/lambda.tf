# IAM Role for Lambda with SSM Permissions (no CloudWatch)
resource "aws_iam_role" "lambda_exec_role" {
  name = "lambda_exec_with_ssm${var.isProd}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "lambda_exec_with_ssm - ${var.environment}"
    }
  )
}

# Inline policy for accessing SSM Parameter Store
resource "aws_iam_role_policy" "lambda_ssm_policy" {
  name = "lambda_ssm_access${var.isProd}"
  role = aws_iam_role.lambda_exec_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Action = [
        "ssm:Describe*",
        "ssm:Get*",
        "ssm:List*"
      ],
      Resource = "*"
    }]
  })
}

#allow only APIGateway to invoke the Lambda function
resource "aws_lambda_permission" "allow_apigateway" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.mtl_site_function.function_name
  principal     = "apigateway.amazonaws.com"

  # REST API ARN format
  source_arn = "${aws_api_gateway_rest_api.maruti_site_api.execution_arn}/*/*"
}

# Zip the Backend code
data "archive_file" "backend_lambda_zip" {
  type        = "zip"
  source_dir  = "../Backend/"
  output_path = "${path.module}/../Backend/lambda_function_backend.zip"
}

# Lambda Function
resource "aws_lambda_function" "mtl_site_function" {
  function_name    = "mtl_site_function${var.isProd}"
  runtime          = "nodejs18.x"
  role             = aws_iam_role.lambda_exec_role.arn
  handler          = "api/index.handler"
  filename         = data.archive_file.backend_lambda_zip.output_path
  source_code_hash = data.archive_file.backend_lambda_zip.output_base64sha256
  timeout          = var.lambda_timeout
  description      = "Lambda function for Maruti Site Form Submissions - ${var.environment}"
  environment {
    variables = {
      # Parameterized SSM parameter path based on environment
      SSM_PARAMETER_PATH = "/${var.environment == "prod" ? "production" : "development"}/maruti-site/env"
    }
  }
  tags = merge(
    var.common_tags,
    {
      Name = "mtl_site_function - ${var.environment}"
    }
  )
}

# Lambda@Edge IAM Role and Policy
resource "aws_iam_role" "lambda_edge_role" {
  name = "mtl_lambda_edge_role${var.isProd}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "edgelambda.amazonaws.com"
        }
      }
    ]
  })
  tags = merge(
    var.common_tags,
    {
      Name = "mtl_lambda_edge_role - ${var.environment}"
    }
  )
}

# Zip the Lambda@Edge code
data "archive_file" "edge_lambda_zip" {
  type        = "zip"
  source_file = "${path.module}/lambda_edge_function/index.js"
  output_path = "${path.module}/lambda_edge_function/lambda_edge_code.zip"
}

resource "aws_lambda_function" "edge_redirect" {
  provider      = aws.useast1 # use us-east-1 provider
  function_name = "mtl-edge-redirect${var.isProd}"
  role          = aws_iam_role.lambda_edge_role.arn
  handler       = "index.handler"
  runtime       = "nodejs18.x"
  publish       = true

  filename         = data.archive_file.edge_lambda_zip.output_path
  source_code_hash = data.archive_file.edge_lambda_zip.output_base64sha256

  lifecycle {
    create_before_destroy = true
  }

  description = "Lambda@Edge function for CloudFront viewer-request redirects & rewrites - ${var.environment}"

  tags = merge(
    var.common_tags,
    {
      Name = "mtl_edge_redirect - ${var.environment}"
    }
  )
}