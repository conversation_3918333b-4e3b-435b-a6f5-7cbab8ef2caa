# ------------------------
# REST API Definition
# ------------------------
resource "aws_api_gateway_rest_api" "maruti_site_api" {
  name        = "maruti-site-rest-api${var.isProd}"
  description = "REST API for Maruti site forms (contact-us & AI readiness) (${var.environment})"
  tags        = var.common_tags
}

# ------------------------
# Resources (/contact-us and /ai-readiness)
# ------------------------

# Resource for /contact-us path
resource "aws_api_gateway_resource" "contact_us" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  parent_id   = aws_api_gateway_rest_api.maruti_site_api.root_resource_id
  path_part   = "contact-us"
}

# Resource for /ai-readiness path
resource "aws_api_gateway_resource" "ai_readiness" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  parent_id   = aws_api_gateway_rest_api.maruti_site_api.root_resource_id
  path_part   = "ai-readiness"
}

# ------------------------
# POST Methods
# ------------------------

# POST method for /contact-us (requires API key)
resource "aws_api_gateway_method" "post_contact_us" {
  rest_api_id      = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id      = aws_api_gateway_resource.contact_us.id
  http_method      = "POST"
  authorization    = "NONE"
  api_key_required = true
}

# POST method for /ai-readiness (requires API key)
resource "aws_api_gateway_method" "post_ai_readiness" {
  rest_api_id      = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id      = aws_api_gateway_resource.ai_readiness.id
  http_method      = "POST"
  authorization    = "NONE"
  api_key_required = true
}

# ------------------------
# Lambda Integrations
# ------------------------

# Integration for /contact-us with single Lambda (router-based)
resource "aws_api_gateway_integration" "lambda_contact_us" {
  rest_api_id             = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id             = aws_api_gateway_resource.contact_us.id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.mtl_site_function.invoke_arn
}

# Integration for /ai-readiness with same Lambda
resource "aws_api_gateway_integration" "lambda_ai_readiness" {
  rest_api_id             = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id             = aws_api_gateway_resource.ai_readiness.id
  http_method             = "POST"
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.mtl_site_function.invoke_arn
}

# ------------------------
# Deployment
# ------------------------

# Deployment for current configuration (routes + integrations)
resource "aws_api_gateway_deployment" "maruti_api_deployment" {
  depends_on = [
    aws_api_gateway_integration.lambda_contact_us,
    aws_api_gateway_integration.lambda_ai_readiness
  ]
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  description = "Deployment for Maruti site API (${var.environment})"
}

# ------------------------
# Stage (modern stage management)
# ------------------------
resource "aws_api_gateway_stage" "maruti_api_stage" {
  stage_name    = var.environment
  rest_api_id   = aws_api_gateway_rest_api.maruti_site_api.id
  deployment_id = aws_api_gateway_deployment.maruti_api_deployment.id
  description   = "Stage (${var.environment}) for Maruti site form APIs"
  tags          = var.common_tags
}

# ------------------------
# API Key + Usage Plan
# ------------------------

# API Key for the usage plan
resource "aws_api_gateway_api_key" "maruti_site_api_key" {
  name        = "maruti-site-api-key${var.isProd}"
  description = "API key for accessing Maruti form endpoints (${var.environment})"
  tags        = var.common_tags
}

# Usage plan with rate limiting
resource "aws_api_gateway_usage_plan" "maruti_site_usage_plan" {
  name        = "maruti-site-rate-limit-plan${var.isProd}"
  description = "Usage plan to limit access rate for Maruti form submissions (${var.environment})"
  tags        = var.common_tags

  api_stages {
    api_id = aws_api_gateway_rest_api.maruti_site_api.id
    stage  = aws_api_gateway_stage.maruti_api_stage.stage_name
  }

  throttle_settings {
    burst_limit = var.burst_limit # Max requests at once
    rate_limit  = var.rate_limit  # Max requests per second (sustained rate)
  }
}

# Associate API Key with the usage plan
resource "aws_api_gateway_usage_plan_key" "maruti_site_usage_plan_key" {
  key_id        = aws_api_gateway_api_key.maruti_site_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.maruti_site_usage_plan.id
}

# OPTIONS method for preflight requests
resource "aws_api_gateway_method" "options_contact_us" {
  rest_api_id   = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id   = aws_api_gateway_resource.contact_us.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Mock integration for OPTIONS method
resource "aws_api_gateway_integration" "options_contact_us" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.contact_us.id
  http_method = aws_api_gateway_method.options_contact_us.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Method response to declare CORS headers
resource "aws_api_gateway_method_response" "options_contact_us" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.contact_us.id
  http_method = aws_api_gateway_method.options_contact_us.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }

  response_models = {
    "application/json" = "Empty"
  }
}

# Integration response to actually return CORS headers
resource "aws_api_gateway_integration_response" "options_contact_us" {
  depends_on  = [aws_api_gateway_integration.options_contact_us]
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.contact_us.id
  http_method = aws_api_gateway_method.options_contact_us.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'${var.cors_allowed_origin}'"
  }
}

# OPTIONS method for preflight CORS requests
resource "aws_api_gateway_method" "options_ai_readiness" {
  rest_api_id   = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id   = aws_api_gateway_resource.ai_readiness.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# MOCK integration for OPTIONS
resource "aws_api_gateway_integration" "options_ai_readiness" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.ai_readiness.id
  http_method = aws_api_gateway_method.options_ai_readiness.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

# Method response declaring CORS headers
resource "aws_api_gateway_method_response" "options_ai_readiness" {
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.ai_readiness.id
  http_method = aws_api_gateway_method.options_ai_readiness.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Origin"  = true
  }

  response_models = {
    "application/json" = "Empty"
  }
}

# Integration response to return actual CORS values
resource "aws_api_gateway_integration_response" "options_ai_readiness" {
  depends_on  = [aws_api_gateway_integration.options_ai_readiness]
  rest_api_id = aws_api_gateway_rest_api.maruti_site_api.id
  resource_id = aws_api_gateway_resource.ai_readiness.id
  http_method = aws_api_gateway_method.options_ai_readiness.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'POST,OPTIONS'",
    "method.response.header.Access-Control-Allow-Origin"  = "'${var.cors_allowed_origin}'"
  }
}

# Custom Domain for API Gateway (Edge-Optimized)
resource "aws_api_gateway_domain_name" "custom_domain" {
  # Custom domain name for API Gateway
  domain_name     = var.api_custom_domain_name
  certificate_arn = var.api_gateway_certificate_arn

  endpoint_configuration {
    types = ["EDGE"]
  }

  tags = var.common_tags
}

# Base Path Mapping for Custom Domain → API Gateway Stage
resource "aws_api_gateway_base_path_mapping" "api_mapping" {
  domain_name = aws_api_gateway_domain_name.custom_domain.domain_name
  api_id      = aws_api_gateway_rest_api.maruti_site_api.id
  stage_name  = aws_api_gateway_stage.maruti_api_stage.stage_name
}