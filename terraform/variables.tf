# AWS region to deploy resources in
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "ap-south-1"
}

# Common tags to apply to all resources (e.g., Environment & ManagedBy )
variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
}

variable "environment" {
  description = "This Refers to the Environment of the Project. (dev / prod)"
  type        = string
}

# List of allowed image file extensions
variable "image_extensions" {
  default = ["svg", "jpg", "png", "webp"]
  type    = list(string)
}

# Suffix to distinguish between environments in resource names
variable "isProd" {
  type        = string
  default     = ""
  description = "If Dev then value is null. If Prod then value is '-prod'"
}

# Domain name to be used for the API Gateway custom domain
variable "api_custom_domain_name" {
  description = "Custom domain name for API Gateway"
  type        = string
}

# The custom domain name (alias) to associate with the CloudFront distribution.
variable "cloudfront_custom_domain_name" {
  description = "The custom domain (alias) used for the CloudFront distribution"
  type        = string
}

# The custom domain name (alias) to associate with the CDN CloudFront distribution (Static Assets).
variable "cloudfront_cdn_custom_domain_name" {
  description = "The custom domain (alias) used for the CDN CloudFront distribution (Static Assets)"
  type        = string
}

# ACM Certificate ARN for API Gateway
variable "api_gateway_certificate_arn" {
  description = "ARN of the ACM certificate ARN for the custom domain used by API Gateway"
  type        = string
}

# ACM Certificate ARN for CloudFront
variable "cloudfront_certificate_arn" {
  description = "ARN of the ACM certificate ARN for the custom domain used by CloudFront"
  type        = string
}

# The allowed origin domain for CORS responses.
variable "cors_allowed_origin" {
  description = "The origin allowed for CORS requests (used in Access-Control-Allow-Origin header)"
  type        = string
}

# The burst limit for the API Gateway usage plan
variable "burst_limit" {
  description = "The burst limit for the API Gateway usage plan"
  type        = number # Max requests at once
}

# The rate limit for the API Gateway usage plan
variable "rate_limit" {
  description = "The rate limit for the API Gateway usage plan"
  type        = number # Max requests per second (sustained rate)
}

# Timeout duration (in seconds) for the Lambda function execution
variable "lambda_timeout" {
  description = "Maximum allowed time (in seconds) that the Lambda function can run before timing out."
  type        = number
}