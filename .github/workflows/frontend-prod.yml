name: Deploy Static Next.js Site to S3 with CloudFront Invalidation - Production

on:
  push:
    branches:
      - main
    paths:
      - Frontend/**
      - .github/workflows/frontend-prod.yml
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: ap-south-1
      S3_BUCKET_NAME: mtl-site-prod-environment
      SSM_PARAMETER_PATH: "/production/maruti-site/env"

    steps:
      # ----------------------------------------
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: 🔧 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Fetch env vars from SSM Parameter Store
        shell: bash
        run: |
          # Fetch the single parameter (stored as JSON)
          json=$(aws ssm get-parameter \
            --name "${{ env.SSM_PARAMETER_PATH }}" \
            --with-decryption \
            --query 'Parameter.Value' \
            --output text)

          # Parse JSON and export as env vars
          echo "$json" | jq -r 'to_entries[] | "\(.key)=\(.value)"' |
          while IFS='=' read -r key value; do
            clean_key=$(printf '%s' "$key" | tr -d '\r\n')
            clean_val=$(printf '%s' "$value" | tr -d '\r\n')
            echo "::add-mask::$clean_val"
            echo "${clean_key}=${clean_val}" >> $GITHUB_ENV

            # Echo for debug (Masked)
            echo "Loaded env: $clean_key"
          done

      # ----------------------------------------
      - name: Install Dependencies
        run: npm install
        working-directory: Frontend

      # ----------------------------------------
      - name: Delete Old Sitemap and Robots from S3
        run: |
          aws s3 rm s3://${{ env.S3_BUCKET_NAME }}/sitemap.xml || true
          aws s3 rm s3://${{ env.S3_BUCKET_NAME }}/robots.txt || true
        env:
          AWS_ACCESS_KEY_ID:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:            ${{ env.AWS_REGION }}
        
      - name: Build and Export Static Site
        working-directory: Frontend
        env:
          NEXT_PUBLIC_SITE_URL: ${{ env.NEXT_PUBLIC_SITE_URL }}
        run: |
          echo "Cleaning old sitemap and robots.txt files"
          rm -f out/sitemap.xml out/robots.txt || true
          rm -f public/sitemap.xml public/robots.txt || true

          echo "Building with site URL: $NEXT_PUBLIC_SITE_URL"
          npm run build
          npx next-sitemap
          cp public/sitemap.xml out/sitemap.xml
          cp public/robots.txt out/robots.txt

          echo "Sitemap generation completed for: $NEXT_PUBLIC_SITE_URL"

          # Verify sitemap was generated correctly
          if [ -f "out/sitemap.xml" ]; then
            echo "Sitemap preview (first 5 URLs):"
            head -10 out/sitemap.xml | grep -o 'https://[^<]*' | head -5 || echo "Could not extract URLs from sitemap"
          else
            echo "ERROR: Sitemap file not found!"
            exit 1
          fi

      # ----------------------------------------
      - name: Upload build to S3 using AWS CLI
        run: |
           aws s3 sync out s3://${{ env.S3_BUCKET_NAME }} --delete
        working-directory: Frontend
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ${{ env.AWS_REGION }}

      - name: Invalidate CloudFront Cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.cloudfront_id_production }} \
            --paths "/*"
        env:
          AWS_ACCESS_KEY_ID:        ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY:    ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION:               ${{ env.AWS_REGION }}